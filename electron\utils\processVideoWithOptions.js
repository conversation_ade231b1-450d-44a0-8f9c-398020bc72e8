const path = require('path');
const fs = require('fs');
const { exec } = require('child_process');
const { processVideoSimplified } = require('./videoRendererSimplified');
const { getVideoInfo, getEncoder } = require('../ffmpegHandler');
const {demucs} = require('./demucs');
const { ffmpegManager } = require('../ffmpeg-config');

// example
const _renderConfig = {
  srtItems: [
    {
      index: 1,
      id: 1,
      text: '都说动画界有四大挂臂',
      startTime: 0.02,
      endTime: 1.56,
      start: '00:00:00,020',
      end: '00:00:01,560',
      translatedText: 'Người ta thường nói giới hoạt hình có Tứ Đại Quải Tý',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\nguoi-ta-thuong-noi-gioi1748136742869.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\nguoi-ta-thuong-noi-gioi1748136742869.mp3',
      audioDuration1: 2520,
      isGenerated1: true,
    },
    {
      index: 2,
      id: 2,
      text: '但能稳占据天花板的非德鲁比莫属',
      startTime: 1.56,
      endTime: 3.8,
      start: '00:00:01,560',
      end: '00:00:03,800',
      translatedText: 'Nhưng vị trí đỉnh cao bất khả xâm phạm chắc chắn thuộc về Droopy',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\nhung-vi-tri-dinh-cao-ba1748136744412.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\nhung-vi-tri-dinh-cao-ba1748136744412.mp3',
      audioDuration1: 3288,
      isGenerated1: true,
    },
  ],
  srtPath: 'I:\\ReviewDao\\phim\\hhinh.srt',
  blurAreas: [
    {
      type: 'blur',
      x: 13,
      y: 193,
      width: 119,
      height: 98,
      timeStart: 0,
      timeEnd: 220.966667,
    },
    {
      type: 'delogo',
      x: 472.40625,
      y: 123,
      width: 129,
      height: 140,
      timeStart: 0,
      timeEnd: 220.966667,
    },
    {
      type: 'subtitle',
      x: 23.40625,
      y: 391,
      width: 574,
      height: 50,
      timeStart: 0,
      timeEnd: 220.966667,
    },
  ],
  timeRange: {
    start: 0,
    end: 220.966667,
  },
  options: {
    textAnimation: {
      enabled: true,
      fontSize: 24,
      color: '#ffffff',
      value: 'Hello World',
    },
    textSubtitle: {
      enabled: true,
      fontSize: 24,
      color: '#ffffff',
    },
    logo: {
      enabled: false,
      position: 'bottom-right',
      size: 'medium',
    },
    audio: {
      backgroundMusic: {
        enabled: false,
        volume: 0.3,
      },
      originalVolume: 0.8,
      holdOriginalAudio: false,
      holdMusicOnly: false,
    },
    output: {
      quality: '1080p',
      frameRate: 30,
    },
  },
};

// Helper function to check process status
async function checkProcessStatus(processId, event, maxWaitTime = 300000) {
  // 5 minutes max
  const type = 'video-task';
  const startTime = Date.now();

  return new Promise((resolve, reject) => {
    const checkInterval = setInterval(async () => {
      try {
        const elapsed = Date.now() - startTime;

        // Check if max wait time exceeded
        if (elapsed > maxWaitTime) {
          clearInterval(checkInterval);
          reject(new Error(`Demucs process timeout after ${maxWaitTime / 1000} seconds`));
          return;
        }

        // Check if process is still in activeProcesses (from utils.js)
        const { getActiveProcesses } = require('../utils');
        const activeProcesses = getActiveProcesses();
        const isProcessActive = activeProcesses.some((p) => p.processId === processId);

        if (!isProcessActive) {
          // Process has completed (no longer in active processes)
          clearInterval(checkInterval);
          console.log('✅ Demucs process completed successfully');
          event?.sender?.send(type, {
            data: '✅ Demucs process completed successfully',
            code: 0,
          });
          resolve({ completed: true, processId });
        } else {
          // Process still running, send progress update
          const progressMsg = `⏳ Demucs running... (${Math.round(elapsed / 1000)}s elapsed)`;
          console.log(progressMsg);
          event?.sender?.send(type, {
            data: progressMsg,
            code: 0,
          });
        }
      } catch (error) {
        clearInterval(checkInterval);
        console.error('❌ Error checking process status:', error);
        event?.sender?.send(type, {
          data: `❌ Error checking process status: ${error.message}`,
          code: 1,
        });
        reject(error);
      }
    }, 2000); // Check every 2 seconds
  });
}

async function holdMusicOnlyHandler(event, videoInput, outputDir) {
  const htdemucsDir = path.basename(videoInput, path.extname(videoInput));
  const noVolcalAudio = path.join(outputDir, '..','htdemucs', htdemucsDir, 'no_vocals.wav');

  // kiểm tra xem noVolcalAudio có tồn tại không
  if (!fs.existsSync(noVolcalAudio)) {
    console.log('🎵 No vocals audio not found, running demucs...');
    event?.sender?.send('video-task', {
      data: '🎵 Starting Demucs to separate vocals from music...',
      code: 0,
    });

    try {
      // Call demucs and get the process ID
      const demucsResult = await demucs(event, { fileInput: videoInput });

      if (!demucsResult.success) {
        throw new Error(demucsResult.error || 'Failed to start Demucs process');
      }

      console.log('🚀 Demucs process started with ID:', demucsResult.processId);
      event?.sender?.send('video-task', {
        data: `🚀 Demucs process started (ID: ${demucsResult.processId})`,
        code: 0,
      });

      // Wait for demucs to complete
      // wait 50 min max
      const maxWaitTime = 300000 * 10
      await checkProcessStatus(demucsResult.processId, event, maxWaitTime);

      // Verify the output file was created
      if (!fs.existsSync(noVolcalAudio)) {
        throw new Error(`Demucs completed but output file not found: ${noVolcalAudio}`);
      }

      console.log('✅ Demucs completed successfully, output file created:', noVolcalAudio);
      event?.sender?.send('video-task', {
        data: '✅ Demucs completed successfully',
        code: 0,
      });
    } catch (error) {
      console.error('❌ Demucs process failed:', error);
      event?.sender?.send('video-task', {
        data: `❌ Demucs failed: ${error.message}`,
        code: 1,
      });
      throw error;
    }
  } else {
    console.log('✅ No vocals audio file already exists:', noVolcalAudio);
    event?.sender?.send('video-task', {
      data: '✅ Using existing separated audio file',
      code: 0,
    });
  }

  // Create the final video with music only
  console.log('🎬 Creating video with music only...');
  event?.sender?.send('video-task', {
    data: '🎬 Creating video with music only...',
    code: 0,
  });

  const outputPath = path.join(outputDir, 'output_music_only.mp4');
  const encoder = await getEncoder();
  const cmd = `${ffmpegManager.ffmpegPath} -i "${videoInput}" -i "${noVolcalAudio}" -map 0:v -map 1:a -c:v ${encoder} -c:a aac -shortest -y "${outputPath}"`;

  console.log('📝 FFmpeg command:', cmd);
  await execPromise(cmd);

  console.log('✅ Music-only video created:', outputPath);
  event?.sender?.send('video-task', {
    data: '✅ Music-only video created successfully',
    code: 0,
  });

  return outputPath;
}



function generateDirectionExprSlow(start, segment, directionType = 'random') {
  const half = segment / 2;
  const progress = `(abs(mod(t-${start}\\,${segment})-${half})/${half})`;

  const directionMap = {
    updown: [2, 3],
    leftright: [0, 1],
    diagonal: [4, 5, 6, 7],
    all: [0, 1, 2, 3, 4, 5, 6, 7],
    random: [0, 1, 2, 3, 4, 5, 6, 7],
  };

  const validDirections = directionMap[directionType] || directionMap.random;
  const direction = validDirections[Math.floor(Math.random() * validDirections.length)];

  switch (direction) {
    case 0:
      return { x: `(w-text_w)*${progress}`, y: `(h-text_h)/2` }; // Left to Right
    case 1:
      return { x: `(w-text_w)*(1-${progress})`, y: `(h-text_h)/2` }; // Right to Left
    case 2:
      return { x: `(w-text_w)/2`, y: `(h-text_h)*${progress}` }; // Top to Bottom
    case 3:
      return { x: `(w-text_w)/2`, y: `(h-text_h)*(1-${progress})` }; // Bottom to Top
    case 4:
      return { x: `(w-text_w)*${progress}`, y: `(h-text_h)*${progress}` }; // TL → BR
    case 5:
      return { x: `(w-text_w)*(1-${progress})`, y: `(h-text_h)*${progress}` }; // TR → BL
    case 6:
      return { x: `(w-text_w)*${progress}`, y: `(h-text_h)*(1-${progress})` }; // BL → TR
    case 7:
      return { x: `(w-text_w)*(1-${progress})`, y: `(h-text_h)*(1-${progress})` }; // BR → TL
  }
}

// Combined handler for blur and text animation - SINGLE FFmpeg pass
async function applyVideoEffects(event, videoInput, outputDir, blurAreas, textAnimation, textSubtitle, videoInfo, options) {
  // Check which effects are enabled
  const shouldApplyBlur = blurAreas.length > 0 && textSubtitle?.enabled;
  const shouldApplyTextAnimation = textAnimation;
  const { flipVideo, scaleFactor } = options.output || {};

  if (!shouldApplyBlur && !shouldApplyTextAnimation && !flipVideo && scaleFactor == 1) {
    return videoInput; // No effects to apply
  }

  console.log('Applying combined video effects in single FFmpeg pass');

  // Get flip video setting for text animation
  const {width,height} = options?.videoInfo || {}
  const flipFilter = flipVideo ? "hflip" : "";
  // const scaleFilter = `scale=ceil((iw*${scaleFactor})/2)*2:ceil((ih*${scaleFactor})/2)*2`;
  const zoom = scaleFactor
  const scaledW = Math.ceil(width * zoom);
  const scaledH = Math.ceil(height * zoom);
  const zoomFilter = `scale=${scaledW}:${scaledH},crop=${width}:${height}`;
  const videoFilters = [flipFilter, zoomFilter].filter(Boolean).join(", ");
  console.log(`🔄 FlipVideo setting: ${flipVideo}, options.output:`, options?.output);

  const outputFinal = path.basename(videoInput, path.extname(videoInput));
  const outputVideo = path.join(outputDir, `${outputFinal}_effects.mp4`);

  // Build combined filter complex
  let filterComplex = '[0:v]';
  const filters = [];

  // Add blur filters if enabled - COPY Y NGUYÊN từ blurAreasHandler
  if (shouldApplyBlur) {
    const { fps, total_frames, duration } = videoInfo;
    const getFrameNumber = (timeInSeconds) => {
      const frame = Math.round(timeInSeconds * fps);
      // Đảm bảo frame number không vượt quá tổng số frame
      return Math.min(frame, total_frames - 1);
    };

    const blurFilters = blurAreas.map((box, index) => {
      const videoCoords = box.videoCoords;
      const startFrame = getFrameNumber(box.timeStart);
      const endFrame = box.timeEnd === duration ? total_frames - 1 : getFrameNumber(box.timeEnd);

      let enableExpr;
      if (box.timeStart === 0 && box.timeEnd === duration) {
        enableExpr = '1'; // Luôn bật nếu áp dụng cho toàn bộ video
      } else if (box.timeStart === 0) {
        enableExpr = `lte(n,${endFrame})`; // Từ đầu đến frame cụ thể
      } else if (box.timeEnd === duration) {
        enableExpr = `gte(n,${startFrame})`; // Từ frame cụ thể đến cuối
      } else {
        enableExpr = `between(n,${startFrame},${endFrame})`; // Giữa khoảng frame
      }

      return `delogo=enable='${enableExpr}':x=${Math.round(videoCoords.x)}:y=${Math.round(videoCoords.y)}:w=${Math.round(
        videoCoords.width,
      )}:h=${Math.round(videoCoords.height)}`;
    });

    filters.push(...blurFilters);
  }
  // Add video filters (flip, zoom) if enabled
  if (videoFilters) {
    filters.push(videoFilters);
  }
  // Add text animation filter if enabled - COPY Y NGUYÊN từ textAnimationRandomHandler
  if (shouldApplyTextAnimation) {
    const { fontSize, color, value, opacity = 0.5, directionType = 'random', textSpeed = 100, fontPath } = textAnimation;
    const { duration } = videoInfo;

    const segment = textSpeed; // 120s mỗi đoạn
    const count = Math.ceil(duration / segment);
    let textFilters = '';
    const fadeTime = 1;

    // Build font parameter for drawtext
    const fontParam = fontPath && !fontPath.includes('Arial') && !fontPath.includes('Helvetica')
      ? `:fontfile='${fontPath}'`
      : '';

    for (let i = 0; i < count; i++) {
      const start = i * segment;
      const end = Math.min((i + 1) * segment, duration);
      const { x, y } = generateDirectionExprSlow(start, segment, directionType);

      // Mirror text coordinates if video is flipped to keep text readable
      const textX = flipVideo ? `w-text_w-(${x})` : x;
      const textY = y;

      const alpha = `if(lt(t\\,${start}+${fadeTime}),(t-${start})/${fadeTime},if(lt(t\\,${end}-${fadeTime}),1,(${end}-t)/${fadeTime}))`;

      textFilters += `drawtext=text='${value}':fontsize=${fontSize}:fontcolor=${color}@${opacity}:alpha='${alpha}'${fontParam}:`;
      textFilters += `x='${textX}':y='${textY}':enable='between(t\\,${start}\\,${end})',`;
    }

    if (!textFilters) {
      // Mirror fallback text coordinates if video is flipped to keep text readable
      const fallbackX = flipVideo ?
        `w-text_w-((w-text_w)*abs(mod(t\\,180)-90)/90)` :
        `(w-text_w)*abs(mod(t\\,180)-90)/90`;
      textFilters = `drawtext=text='${value}':fontsize=${fontSize}:fontcolor=${color}@${opacity}${fontParam}:x='${fallbackX}':y='(h-text_h)*abs(mod(t\\,180)-90)/90'`;
    }

    textFilters = textFilters.replace(/,$/, '');
    filters.push(textFilters);
  }



  // Combine all filters
  filterComplex += filters.join(',') + '[vout]';

  // Write filter to file
  const filterFile = path.join(outputDir, 'combined_effects_filter.txt');
  fs.writeFileSync(filterFile, filterComplex);

  // Single FFmpeg command for all effects
  const encoder = await getEncoder();
  const cmd = `${ffmpegManager.ffmpegPath} -i "${videoInput}" -filter_complex_script "${filterFile}" -map "[vout]" -map 0:a? -c:v ${encoder} -c:a copy -y "${outputVideo}"`;

  await execPromise(cmd);
  fs.unlinkSync(filterFile);

  console.log('✅ Combined effects applied in single pass:', outputVideo);
  return outputVideo;
}

async function processVideoWithOptions(event, renderConfig = {}) {
  try {
    let videoInput = renderConfig.srtPath.replace('-ocr.srt', '.mp4').replace('.srt', '.mp4');
    const videoInfo = await getVideoInfo(event, videoInput);
    renderConfig.options.videoInfo = videoInfo;
    const outputDir = path.dirname(videoInput);
    const outputFinal = path.basename(videoInput, path.extname(videoInput)) + '_final.mp4';
    const outputVideo = path.join(outputDir, outputFinal);
    const holdMusicOnly = renderConfig?.options?.audio?.holdMusicOnly || false;
    const textAnimation = renderConfig?.options?.textAnimation?.enabled || false;
    const blurAreas = renderConfig?.blurAreas || [];
    const workDirTemp = path.join(outputDir, '_temp');
    if (!fs.existsSync(workDirTemp)) fs.mkdirSync(workDirTemp, { recursive: true });

    if (holdMusicOnly) {
      console.log('Extracting music only');
      videoInput = await holdMusicOnlyHandler(event, videoInput, workDirTemp);
      console.log('Music only extracted', videoInput);
    }
    // Apply video effects (blur and/or text animation) in one pass
    videoInput = await applyVideoEffects(
      event,
      videoInput,
      workDirTemp,
      blurAreas,
      renderConfig?.options?.textAnimation,
      renderConfig?.options.textSubtitle,
      videoInfo,
      renderConfig?.options
    );
    const result = await processVideoSimplified(
      event,
      videoInput,
      renderConfig.srtItems,
      workDirTemp,
      outputVideo,
      renderConfig.options,
    );
    // console.log('Result:', result);
    return result;
  } catch (error) {
    console.error('Error processing video with options:', error);
    throw error;
  }
}

const execPromise = (cmd) =>
  new Promise((resolve, reject) => {
    exec(cmd, (err, stdout, stderr) => {
      if (err) {
        console.error('❌ FFmpeg error:', err);
        console.error(stderr);
        return reject(err);
      }
      resolve();
    });
  });

module.exports = {
  processVideoWithOptions,
};
